# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-07-29 11:07:02 - Log of updates made.

*

## Coding Patterns

* **Langchain 工具封装模式**：使用 @tool 装饰器将现有业务逻辑封装为 AI 可调用的工具
  - 保持与原有 CLI 命令的参数一致性
  - 统一的错误处理和返回格式（Dict[str, Any]）
  - 数据库会话管理的标准化模式（try-except-finally）

## Architectural Patterns

* **工具模块化设计**：按功能域分组工具（任务、分类、标签）
  - 提供 ALL_TOOLS、TASK_TOOLS、CATEGORY_TOOLS、TAG_TOOLS 等预定义工具集
  - 支持灵活的工具组合和选择性绑定

## Testing Patterns

*