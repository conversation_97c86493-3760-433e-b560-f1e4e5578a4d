from typing import Annotated, TypedDict
from langchain.chat_models import init_chat_model
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers.openai_tools import PydanticToolsParser


@tool
def add(a: int, b: int) -> int:
    """Adds a and b."""
    return a + b


@tool
def multiply(a: int, b: int) -> int:
    """Multiplies a and b."""
    return a * b


model = ChatOpenAI(
    model="qwen-turbo",
    api_key="sk-6e41e91d9c2e498f8cf0fb46505e17f1",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

tool_map = {
    "add": add,
    "multiply": multiply,
}

model = model.bind_tools([add, multiply])
tools_parser = PydanticToolsParser(tools=[add, multiply])
messages = [SystemMessage("Using tools for arithmetic")]

while True:
    user_input = input("User: ")
    if user_input == "exit":
        break

    messages.append(HumanMessage(user_input))
    response = model.invoke(messages)
    messages.append(response)
    if response.tool_calls:
        for tool_call in response.tool_calls:
            tool_output = tool_map[tool_call["name"]].invoke(tool_call)
            messages.append(tool_output)
            args = tool_call["args"]
            args_str = ", ".join(f"{k}={v}" for k, v in args.items())
            print(
                f"Tool: {tool_call['name']}({args_str}) output: {tool_output.content}"
            )
        response = model.invoke(messages)
        messages.append(response)
        print(f"AI: {response.content}")
    else:
        print(f"AI: {response.content}")
