import json
from typing import Annotated, TypedDict
from langchain.chat_models import init_chat_model
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage,trim_messages
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers.openai_tools import PydanticToolsParser
from todo.langchain_tools import ALL_TOOLS

def pretty_print_json(json_str: str, indent: int = 2, sort_keys: bool = False) -> str:
    """将 JSON 字符串格式化为美观的字符串"""
    try:
        # 解析 JSON 字符串
        parsed_json = json.loads(json_str)
        # 转换为格式化的字符串（缩进 2 空格，保留原始键顺序）
        return json.dumps(parsed_json, indent=indent, sort_keys=sort_keys, ensure_ascii=False)
    except json.JSONDecodeError as e:
        return json_str  # 返回原始字符串或根据需要处理错误
    
SYSTEM_PROMPT = """
你是专注于待办系统管理的 AI 助手，核心职责是通过调用系统提供的工具，协助用户高效完成任务、分类、标签的全流程管理。你需要理解用户需求，精准匹配工具功能，确保操作准确且反馈清晰。
"""

TOOLS_DICT = {tool.name: tool for tool in ALL_TOOLS}

model = ChatOpenAI(
    model="qwen3-coder-plus",
    api_key="sk-6e41e91d9c2e498f8cf0fb46505e17f1",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)



model = model.bind_tools(ALL_TOOLS)
messages = [SystemMessage(SYSTEM_PROMPT)]

while True:
    user_input = input("User: ")
    if user_input == "exit":
        break

    messages.append(HumanMessage(user_input))
    response = model.invoke(messages)
    messages.append(response)
    if response.tool_calls:
        for tool_call in response.tool_calls:
            tool_output = TOOLS_DICT[tool_call["name"]].invoke(tool_call)
            messages.append(tool_output)
            args = tool_call["args"]
            args_str = ", ".join(f"{k}={v}" for k, v in args.items())
            print(
                f"Tool: {tool_call['name']}({args_str}) \n{pretty_print_json(tool_output.content)}"
            )
        response = model.invoke(messages)
        messages.append(response)
        print(f"AI: {response.content}")
    else:
        print(f"AI: {response.content}")

    # 消息修剪
    trim_messages(messages, 4096)


